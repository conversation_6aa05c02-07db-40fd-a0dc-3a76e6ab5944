"use client";

import * as React from "react";
import { cn } from "./lib/utils";

export interface InventoryCardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Name of the inventory item */
  name: string;
  /** Description of the inventory item */
  description: string;
  /** Current quantity */
  quantity: number;
  /** Unit of measurement */
  unit?: string;
  /** Material type identifier */
  materialType?: string;
  /** Optional image URL */
  imageUrl?: string;
  /** Click handler */
  onCardClick?: () => void;
}

const InventoryCard = React.forwardRef<HTMLDivElement, InventoryCardProps>(
  ({
    className,
    name,
    description,
    quantity,
    unit = "",
    materialType,
    imageUrl,
    onCardClick,
    ...props
  }, ref) => {
    return (
      <div
        ref={ref}
        onClick={onCardClick}
        className={cn(
          "w-full border border-[var(--color-stroke)] rounded-[var(--radius-8)] bg-[var(--color-background-primary)] p-3 cursor-pointer hover:opacity-80 transition-opacity flex gap-3",
          className
        )}
        {...props}
      >
        {/* Image Container */}
        <div className="aspect-square h-full bg-[var(--color-background-secondary)] rounded-[var(--radius-8)] flex items-center justify-center flex-shrink-0">
          {imageUrl ? (
            <img
              src={imageUrl}
              alt={name}
              className="w-full h-full object-cover rounded-[var(--radius-8)]"
            />
          ) : (
            <div className="w-8 h-8 bg-[var(--color-text-secondary)] rounded"></div>
          )}
        </div>

        {/* Text Content */}
        <div className="flex-1 flex flex-col justify-between min-h-0">
          {/* Item Name with Material Type */}
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-inter font-semibold text-base text-[var(--color-text-primary)] overflow-hidden text-ellipsis whitespace-nowrap flex-1 min-w-0">
              {name}
            </h3>
            {materialType && (
              <div className="rounded bg-[var(--color-background-secondary)] px-2 py-0.5 ml-2 flex-shrink-0">
                <span className="font-inter font-semibold text-[10px] text-[var(--color-text-secondary)]">
                  {materialType}
                </span>
              </div>
            )}
          </div>

          {/* Description */}
          <p className="font-inter text-xs font-normal text-[var(--color-text-secondary)] mb-2 overflow-hidden text-ellipsis whitespace-nowrap">
            {description}
          </p>

          {/* Quantity */}
          <div className="font-inter text-xs font-medium text-[var(--color-text-primary)]">
            Qty: {quantity}{unit && ` ${unit}`}
          </div>
        </div>
      </div>
    );
  }
);

InventoryCard.displayName = "InventoryCard";

export { InventoryCard };
